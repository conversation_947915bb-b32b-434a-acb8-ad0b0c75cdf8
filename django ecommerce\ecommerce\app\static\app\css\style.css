*{
padding: 0;
margin: 0;
box-sizing: border-box;
font-family: 'Times New Roman', Times, serif;
cursor: url("data:image/svg+xml,%3Csvg version='1.1' id='Layer_1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' width='24px' height='24px' viewBox='0 0 512 512' style='enable-background:new 0 0 512.011 512.011;' xml:space='preserve'%3E %3Cpath fill='DeepSkyBlue' d='M434.215,344.467L92.881,3.134c-4.16-4.171-10.914-4.179-15.085-0.019  c-2.011,2.006-3.139,4.731-3.134,7.571v490.667c0.003,4.382,2.685,8.316,6.763,9.92c4.081,1.603,8.727,0.545,11.712-2.667  l135.509-145.92h198.016c5.891,0.011,10.675-4.757,10.686-10.648C437.353,349.198,436.226,346.473,434.215,344.467z'/%3E %3C/svg%3E"), pointer;
}
::-webkit-scrollbar{
width: 10px;
cursor: not-allowed;
}        
::-webkit-scrollbar-track{
background: #0275d8;
cursor: not-allowed;
}        
::-webkit-scrollbar-thumb{
background: white;
border-radius: 10px;
}        
::-webkit-scrollbar-thumb:hover{
background: #f0ad4e;
}