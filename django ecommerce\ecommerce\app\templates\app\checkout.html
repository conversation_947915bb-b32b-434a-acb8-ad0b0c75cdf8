{% extends "app/base.html" %}
{% load static %}
{% block title %}Checkout{% endblock title %}
{% block main-content %}
<div class="container">
{% if messages %}
{% for msg in messages %}
<div class="alert aler-danger" role="alert">
    {{msg}}
</div>
{% endfor %}
{% endif %}
<div class="row mt-5">
<div class="col-sm-6">
<h4>Summary</h4>
<hr>
{% for item in cart_items %}
<div class="card mb-2">
<div class="card-body">
<h5 class="">Product : {{item.product.title}}</h5>
<p>Quantity : {{item.quantity}}</p>
<p class="fw-bold">Price : {{item.product.discount_price}}</p>
</div>
</div>
{% endfor %}
<p class="fw-bold">Total Cost + 20 = {{totalamount}}</p>
</div>
<div class="col-sm-4 offset-sm-1">
<h4>Select Address</h4>
<hr>
<form method="post" id="myform">
{% csrf_token %}
{% for ad in add %}
<div class="card">
<div class="card-body">
<h5>{{ad.name}}</h5>
<p>{{ad.mobile}}</p>
<p>{{ad.locality}} {{ad.city}} {{ad.state}} {{ad.zipcode}}</p>
</div>
</div>
<div class="form-check mt-2 mb-5">
<input class="form-check-input" type="radio" name="custid" id="custadd{{forloop.counter}}" value="{{ad.id}}">
<label class="form-check-label fw-bold" for="custadd{{forloop.counter}}">Address {{forloop.counter}}</label>
</div>
{% endfor %}
<div class="form-check mb-3">
<label for="totamount" class="form-label">Total Amount</label>
<input type="number" class="form-control" name="totamount" value="{{totalamount}}" readonly>
</div>
<div class="text-end">

<a href="{% url 'checkout' %}" id="rzp-button1" type="submit" class="btn btn-warning mt-3 px-5 fw-bold">Payment</a>
</div>
</form>
</div>
</div>
</div>
{% endblock main-content %}
