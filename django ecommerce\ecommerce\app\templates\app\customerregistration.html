{% extends "app/base.html" %}
{% load static %}
{% block title %}Customer Registration{% endblock title %}
{% block main-content %}
    <div class="container">
    <div class="row m-5">
    <div class="col-sm-6 offset-sm-3">
        <h3 class="text-center text-danger">Customer Registration</h3>
        <form action="" method="post" novalidate class="shadow p-5">
        {% csrf_token %}
        {% for fm in form %}
            {{fm.label_tag}} {{fm}} <small class="text-danger">{{fm.errors}}</small>
            <br>
        {% endfor %}
            <input type="submit" value="Submit" class="btn btn-primary">
    <div class="text-center text-primary fw-bold">
        <small>You Are Already A User?<a href="{% url 'login' %}" class="text-danger text-decoration-none m-1">Login Now</a></small>
    </div>
    {% if form.non_field_errors %}
        {% for error in form.non_field_errors %}
            <p class="alert alert-danger my-3">{{error}}</p>
        {% endfor %}
        {% endif %}
        {% if messages %}
            {% for msg in messages %}
                <div class="alert alert-{{msg.tags}}" role="alert">{{msg}}</div>
            {% endfor %}
        {% endif %}    
    </form>
    </div>
    </div>
    </div>
{% endblock main-content %}