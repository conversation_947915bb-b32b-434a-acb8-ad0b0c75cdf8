# Generated by Django 5.0 on 2024-01-24 09:32

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('locality', models.CharField(max_length=200)),
                ('city', models.CharField(max_length=25)),
                ('mobile', models.IntegerField(default=0)),
                ('zipcode', models.IntegerField()),
                ('state', models.CharField(choices=[('<PERSON>na', '<PERSON>na'), ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'), ('<PERSON><PERSON><PERSON>', '<PERSON>fyon'), ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>r<PERSON>'), ('<PERSON><PERSON>ya', '<PERSON><PERSON><PERSON>'), ('Ankara', 'Ankara'), ('<PERSON><PERSON><PERSON>', '<PERSON>tal<PERSON>'), ('Art<PERSON>', '<PERSON>vin'), ('Aydın', 'Aydın'), ('Balıkesir', 'Balıkesir'), ('Bilecik', 'Bilecik'), ('Bingöl', 'Bingöl'), ('Bitlis', 'Bitlis'), ('Bolu', 'Bolu'), ('Burdur', 'Burdur'), ('Bursa', 'Bursa'), ('Çanakkale', 'Çanakkale'), ('Çankırı', 'Çankırı'), ('Çorum', 'Çorum'), ('Denizli', 'Denizli'), ('Diyarbakır', 'Diyarbakır'), ('Edirne', 'Edirne'), ('Elazığ', 'Elazığ'), ('Erzincan', 'Erzincan'), ('Erzurum', 'Erzurum'), ('Eskişehir', 'Eskişehir'), ('Gaziantep', 'Gaziantep'), ('Giresun', 'Giresun'), ('Gümüşhane', 'Gümüşhane'), ('Hakkari', 'Hakkari'), ('Hatay', 'Hatay'), ('Isparta', 'Isparta'), ('Mersin', 'Mersin'), ('İstanbul', 'İstanbul'), ('İzmir', 'İzmir'), ('Kars', 'Kars'), ('Kastamonu', 'Kastamonu'), ('Kars', 'Kars'), ('Kırklareli', 'Kırklareli'), ('Eskişehir', 'Eskişehir'), ('Kocaeli', 'Kocaeli'), ('Konya', 'Konya'), ('Kütahya', 'Kütahya'), ('Malatya', 'Malatya'), ('Manisa', 'Manisa'), ('Kahramanmaraş', 'Kahramanmaraş'), ('Mardin', 'Mardin'), ('Muğla', 'Muğla'), ('Muş', 'Muş'), ('Nevşehir', 'Nevşehir'), ('Niğde', 'Niğde'), ('Ordu', 'Ordu'), ('Rize', 'Rize'), ('Sakarya', 'Sakarya'), ('Samsun', 'Samsun'), ('Siirt', 'Siirt'), ('Sinop', 'Sinop'), ('Sivas', 'Sivas'), ('Tekirdağ', 'Tekirdağ'), ('Tokat', 'Tokat'), ('Trabzon', 'Trabzon'), ('Tunceli', 'Tunceli'), ('Şanlıurfa', 'Şanlıurfa'), ('Uşak', 'Uşak'), ('Van', 'Van'), ('Yozgat', 'Yozgat'), ('Zonguldak', 'Zonguldak'), ('Aksaray', 'Aksaray'), ('Bayburt', 'Bayburt'), ('Karaman', 'Karaman'), ('Kırkkale', 'Kırıkkale'), ('Batman', 'batman'), ('Şırnak', 'Şırnak'), ('Bartın', 'Bartın'), ('Ardahan', 'Ardahan'), ('Iğdır', 'Iğdır'), ('Yalova', 'Yalova'), ('Karabük', 'Karabük'), ('Kilis', 'Kilis'), ('Osmaniye', 'Osmaniye'), ('Düzce', 'Düzce')], max_length=100)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
