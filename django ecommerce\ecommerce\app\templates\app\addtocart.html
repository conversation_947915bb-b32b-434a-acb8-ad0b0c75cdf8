{% extends "app/base.html" %}
{% load static %}
{% block title %}Add To Cart{% endblock title %}
{% block main-content %}
<div class="container my-5">
<div class="row">
{% if cart %}
<h1 class="text-center mb-5 text-primary">Shopping Cart</h1>
<div class="col-sm-8">
<div class="card">
<div class="card-body">
<h3>Your Cart</h3>
{% for item in cart %}
<div class="row"  id="display-products">
<div class="col-sm-3 text-center align-self-center"><img src="{{item.product.product_image.url}}" alt="" class="img-fluid img-thumbnail shadow-sm" height="250" width="200"></div>
<div class="col-sm-9">
<div>
<h5>{{item.product.title}}</h5>
<p class="mb-2 text-muted small">{{item.product.description}}</p>
<div class="my-3">
<label for="quantity">Quantity : </label>
<a class="minus-cart btn" pid={{item.product.id}}><i class="fas fa-minus-square fa-lg"></i></a>
    <span id="quantity">{{item.quantity}}</span>
<a class="plus-cart btn" pid={{item.product.id}}><i class="fas fa-plus-square fa-lg"></i></a>
</div>
<div class="d-flex justify-content-between">
<a href="#" class="remove-cart btn btn-sm btn-secondary mr-3" pid={{item.product.id}}>Remove Item</a>
<p class="mb-0"><span><strong>Price : {{item.product.discount_price}} </strong></span></p>
</div>
</div>
</div>
</div>
<hr class="text-muted">
{% endfor %}
</div>
</div>
</div>


<div class="col-sm-4">
<div class="card">
<div class="card-body">
<h3>The Total Amount of</h3>
<ul class="list-group">
<li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 pb-0">Amount<span id="amount">{{amount}}</span></li>
<li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 mb-3">Shipping<span>20</span></li>
<li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 mb-3">
<div>
<strong>Total</strong> <small>{including SGT}</small>
</div>
<span id="totalamount"><strong>{{totalamount}}</strong></span>
</li>
</ul>
<div class="d-grid"><a class="btn btn-primary" id="sepet" href="{% url 'paymentcompleted' %}">Place Your Order</a></div>
</div>
</div>
</div>
{% else %}
<h1 class="text-center mb-5 text-danger">Cart Is Empty</h1>
{% endif %}
</div>
</div>
{% endblock main-content %}