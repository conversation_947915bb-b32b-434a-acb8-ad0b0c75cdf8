{% extends "app/base.html" %}
{% load static %}
{% block title %}Change Password{% endblock title %}
{% block main-content %}
<div class="container my-5 text-center">
    <div class="row text-center">
    <h3 class="text-primary text-danger text-capitalize ms-5">Welcome <p class="text-primary">{{request.user.username}}</p></h3>
        <div class="col-sm-2 border-end">
            <ul>
                <li class="d-grid"><a href="#" class="btn btn-primary">Change Password</a></li>
            </ul>
        </div>
        <div class="col-sm-9 offset-sm-1 text-info">
            <form action="" method="post" novalidate class="shadow p-5">
                {% csrf_token %}
                {% for fm in form %}
                    {{fm.label_tag}} {{fm}} <small class="text-danger">{{em.errors}}</small>
                {% endfor %}
                <button class="btn btn-primary mt-3" type="submit">Submit</button>
                {% if form.non_field_errors %}
                    {% for error in form.non_field_errors %}
                        <p class="alert alert-danger my-3">{{error}}</p>
                    {% endfor %}
                {% endif %}
            </form>
        </div>
        </div>
</div>
{% endblock main-content %}