{% extends "app/base.html" %}
{% load static %}
{% block title %}Category{% endblock title %}
{% block main-content %}
<div class="container my-5">
    <div class="row">
        <div class="col col-sm-3">
            <div class="list-group">
                {% for val in title %}
                <a href="{% url 'category-title' val.title %}" class="list-group-item list-group-item-action text-center text-warning bg-primary" aria-current="true">
                    {{val.title}}
                </a>
                {% endfor %}
            </div>
        </div>
        <div class="col col-sm-9">
            <div class="row">
                {% for prod in product %}
                <div class="col text-center mb-4">
                    <a href="{% url 'product-detail' prod.id %}" class="btn">
                        <div>
                            <img src="{{prod.product_image.url}}" width="300px" height="200px"/>
                            <div class="fw-bold text-primary">{{prod.title}}</div>
                            <div class="fw-bold text-warning">
                                Current Price : {{prod.discount_price}}
                                <small class="fw-light text-decoration-line-through text-danger">{{prod.selling_price}}</small>
                            </div>
                        </div>
                    </a>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock main-content %}