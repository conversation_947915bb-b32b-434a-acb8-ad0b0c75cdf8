{% extends "app/base.html" %}
{% load static %}
{% block title %}Login{% endblock title %}
{% block main-content %}
<div>
    <div class="row my-3 text-center">
        <div class="col-sm-6 offset-sm-3">
            <h3 class="text-center text-danger">Login</h3>
            <form method="post" novalidate class="shadow p-5">
                {% csrf_token %}
                {% for fm in form %}
                    {{fm.label_tag}}{{fm}} <small class="text-danger">{{fm.errors}}</small>
                {% endfor %}
                <small><a href="{% url 'password_reset' %}" class="text-danger text-center">You Forgot Your Password?</a></small><br>
                <input type="submit" class="btn btn-primary mt-4" value="Login">
                <div class="text-center text-primary fw-bold mt-3"><small>New User?<a href="{% url 'customerregistration' %}" class="text-danger">Create An Account</a></small></div>
                {% if form.non_field_errors %}
                    {% for error in form.non_field_errors %}
                        <p class="alert alert-danger my-3">{{error}}</p>
                    {% endfor %}
                {% endif %}
            </form>
        </div>
    </div>
</div>
{% endblock main-content %}