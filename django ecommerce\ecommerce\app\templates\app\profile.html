{% extends "app/base.html" %}
{% load static %}
{% block title %}Your Profile{% endblock title %}
{% block main-content %}
<div class="container my-5 text-center">
<div class="row">
        <h3 class="text-danger">Welcome Dear <span class="text-capitalize text-primary">{{request.user}}</span></h3>
    <div class="col-sm-2 border-end">
        <ul class="list-unstyled">
            <li class="d-grid"><a href="{% url 'profile' %}" class="btn btn-primary">Profile</a></li>
        </ul>
    </div>
    <div class="col-sm-8 offset-sm-1 text-info">
        <form action="" method="post">
            {% csrf_token %}
            {% for fm in form %}
                {{fm.label_tag}} {{fm}} <small class="text-danger">{{fm.errors}}</small>
            {% endfor %}
            <div class="col-12 mt-3">
                <button type="submit" class="btn btn-primary">Submit</button>
            </div>
            {% if form.non_field_errors %}
                {% for error in form.non_field_errors %}
                    <p class="alert alert-danger my-3">{{error}}</p>
                {% endfor %}
            {% endif %}
            {% if messages %}
                {% for msg in messages %}
                    <div class="alert alert-{{msg.tags}}" role="alert">
                        {{msg}}
                    </div>
                {% endfor %}
            {% endif %}
        </form>
    </div>
    </div>
</div>
{% endblock main-content %}