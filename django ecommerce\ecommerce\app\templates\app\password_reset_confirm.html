{% extends "app/base.html" %}
{% load static %}
{% block title %}{% endblock title %}
{% block main-content %}
<div class="container text-center">
    <div class="row my-3">
        <div class="col-sm-6 offset-sm-3">
            <h3 class="text-primary">Password Confirm</h3>
            <hr>
            <form class="shadow py-5" novalidate action="" method="post">
                {% csrf_token %}
                {% for fm in form %}
                    {{fm.label_tag}} {{fm}} <small class="text-danger">{{fm.errors}}</small> <br>
                {% endfor %}
                <input type="submit" class="btn btn-primary mt-4" value="Submit">
                <br>
                {% if form.non_field_errors %}
                    {% for error in form.non_field_errors %}
                        <p class="alert alert-danger my-3">{{error}}</p>
                    {% endfor %}
                {% endif %}
            </form>
        </div>
    </div>
</div>
{% endblock main-content %}