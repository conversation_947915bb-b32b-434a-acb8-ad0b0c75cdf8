{% extends "app/base.html" %}
{% load static %}

{% block title %}Product Detail{% endblock title %}

{% block main-content %}
<div class="container mt-5">
    <div class="row flex justify-content-between">
        <div class="img2 col-lg-5 mt-5">
            <img src="{{product.product_image.url}}" class="image col-xs-6 col-sm-12 col-lg-12 mt-3 text-center w-100 h-75">
        </div>

        <div class="productdetail col-lg-5">
            <h1 class="text-center">{{product.title}}</h1>
            <h5 class="text-warning">Current Price : {{product.discount_price}}</h5>
            <small class="text-decoration-line-through fs-5 text-danger"><del>{{product.selling_price}}</del></small>
            <br>
            <h4 class="text-primary">Product Features</h4>
            <ul>
                <li>{{product.description}}</li>
            </ul>

                <form action="/add-to-cart" class="d-inline">
                    <input type="hidden" name="prod_id" value="{{product.id}}">
                <button type="submit" class="btn btn-danger shadow btn1 btn-lg text-decoration-none text-white px-5 py-2 ms-4">Add To Cart</button>
                </form>
                <a class="btn btn-primary text-white btn-lg shadow px-5 py-2 ms-4" href="{% url 'showcart' %}">Buy Now</a>

                <div class="text-center justify-content-center align-items-center me-5 mt-3">
                {% if wishlist %}
                <a pid={{product.id}} href="{% url 'minuswishlist' %}" class="minus-wishlist btn btn-danger shadow px-5 py-2 ms-4"><i class="fas fa-heart fa-lg"></i></a>
                {% else %}
                <a pid={{product.id}} href="{% url 'pluswishlist' %}" class="plus-wishlist btn btn-success shadow px-5 py-2 ms-4"><i class="fas fa-heart fa-lg"></i></a>
                {% endif %}
                </div>
        </div>

    </div>
</div>
{% endblock main-content %}
